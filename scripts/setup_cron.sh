#!/bin/bash

# Setup daily cron job for Istanbul Pharmacy Scraper
# This script sets up automatic daily execution of the pharmacy scraper

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Define the cron job command
CRON_COMMAND="0 6 * * * cd $PROJECT_DIR && /usr/bin/python3 scripts/pharmacy_scraper.py >> scripts/cron.log 2>&1"

# Function to check if cron job already exists
check_existing_cron() {
    crontab -l 2>/dev/null | grep -q "pharmacy_scraper.py"
    return $?
}

# Function to add cron job
add_cron_job() {
    # Get current crontab
    (crontab -l 2>/dev/null; echo "$CRON_COMMAND") | crontab -

    if [ $? -eq 0 ]; then
        echo "✅ Cron job added successfully!"
        echo "📅 The scraper will run daily at 6:00 AM"
        echo "📁 Logs will be saved to: $SCRIPT_DIR/cron.log"
    else
        echo "❌ Failed to add cron job"
        return 1
    fi
}

# Function to remove cron job
remove_cron_job() {
    crontab -l 2>/dev/null | grep -v "pharmacy_scraper.py" | crontab -

    if [ $? -eq 0 ]; then
        echo "✅ Cron job removed successfully!"
    else
        echo "❌ Failed to remove cron job"
        return 1
    fi
}

# Function to show current cron jobs
show_cron_jobs() {
    echo "Current cron jobs:"
    crontab -l 2>/dev/null | grep -E "(pharmacy|scraper)" || echo "No pharmacy scraper cron jobs found"
}

# Function to test the scraper
test_scraper() {
    echo "🧪 Testing the scraper..."
    cd "$PROJECT_DIR"
    python3 scripts/pharmacy_scraper.py test

    if [ $? -eq 0 ]; then
        echo "✅ Scraper test completed successfully!"
    else
        echo "❌ Scraper test failed!"
        return 1
    fi
}

# Main menu
case "${1:-menu}" in
    "install"|"add")
        echo "🔧 Setting up daily cron job for Istanbul Pharmacy Scraper..."

        if check_existing_cron; then
            echo "⚠️  Cron job already exists!"
            echo "Current cron job:"
            crontab -l | grep "pharmacy_scraper.py"
            echo ""
            read -p "Do you want to replace it? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                remove_cron_job
                add_cron_job
            else
                echo "Keeping existing cron job."
            fi
        else
            add_cron_job
        fi
        ;;

    "remove"|"uninstall")
        echo "🗑️  Removing cron job..."
        if check_existing_cron; then
            remove_cron_job
        else
            echo "No cron job found to remove."
        fi
        ;;

    "status"|"show")
        show_cron_jobs
        ;;

    "test")
        test_scraper
        ;;

    "logs")
        LOG_FILE="$SCRIPT_DIR/cron.log"
        if [ -f "$LOG_FILE" ]; then
            echo "📋 Last 50 lines of cron log:"
            tail -50 "$LOG_FILE"
        else
            echo "No log file found at $LOG_FILE"
        fi
        ;;

    "help"|"--help"|"-h")
        echo "Istanbul Pharmacy Scraper - Cron Setup"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  install, add     - Install daily cron job (runs at 6:00 AM)"
        echo "  remove, uninstall - Remove cron job"
        echo "  status, show     - Show current cron jobs"
        echo "  test             - Test the scraper manually"
        echo "  logs             - Show recent cron execution logs"
        echo "  help             - Show this help message"
        echo ""
        echo "The cron job will:"
        echo "  • Run daily at 6:00 AM"
        echo "  • Scrape Istanbul pharmacies for today and tomorrow"
        echo "  • Save data as JSON files"
        echo "  • Automatically clean up files older than 7 days"
        echo "  • Log all output to scripts/cron.log"
        ;;

    "menu"|*)
        echo "🏥 Istanbul Pharmacy Scraper - Cron Setup"
        echo ""
        echo "What would you like to do?"
        echo "1) Install daily cron job"
        echo "2) Remove cron job"
        echo "3) Show current status"
        echo "4) Test scraper"
        echo "5) View logs"
        echo "6) Help"
        echo ""
        read -p "Enter your choice (1-6): " choice

        case $choice in
            1) $0 install ;;
            2) $0 remove ;;
            3) $0 status ;;
            4) $0 test ;;
            5) $0 logs ;;
            6) $0 help ;;
            *) echo "Invalid choice. Use '$0 help' for usage information." ;;
        esac
        ;;
esac

echo ""
echo "📍 Project directory: $PROJECT_DIR"
echo "📍 Script directory: $SCRIPT_DIR"
