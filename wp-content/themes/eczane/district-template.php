<?php
/**
 * District Template
 * 
 * @package EczaneTheme
 */

// Get district and date from query vars
$district_slug = get_query_var('eczane_district');
$selected_date = get_query_var('eczane_date');

if (!$selected_date) {
    $selected_date = date('Y-m-d');
}

// Get pharmacies for this district
$pharmacies = eczane_get_pharmacies_by_district($district_slug, $selected_date);

// Get district name
global $wpdb;
$table_districts = $wpdb->prefix . 'eczane_districts';
$district = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM $table_districts WHERE slug = %s",
    $district_slug
));

if (!$district) {
    // District not found, redirect to home
    wp_redirect(home_url());
    exit;
}

// Set page title and meta
$page_title = $district->name . ' Nöbetçi Eczaneleri - ' . date('d.m.Y', strtotime($selected_date));
$page_description = $district->name . ' ilçesindeki ' . date('d.m.Y', strtotime($selected_date)) . ' tarihli nöbetçi eczane listesi. Adres, telefon ve yol tarifi bilgileri.';

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo esc_html($page_title); ?> - <?php bloginfo('name'); ?></title>
    <meta name="description" content="<?php echo esc_attr($page_description); ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo esc_attr($page_title); ?>">
    <meta property="og:description" content="<?php echo esc_attr($page_description); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo esc_url(home_url('/ilce/' . $district_slug)); ?>">
    
    <!-- Schema.org -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "<?php echo esc_js($page_title); ?>",
        "description": "<?php echo esc_js($page_description); ?>",
        "url": "<?php echo esc_url(home_url('/ilce/' . $district_slug)); ?>",
        "mainEntity": {
            "@type": "ItemList",
            "name": "<?php echo esc_js($district->name); ?> Nöbetçi Eczaneleri",
            "numberOfItems": <?php echo count($pharmacies); ?>,
            "itemListElement": [
                <?php foreach ($pharmacies as $index => $pharmacy): ?>
                {
                    "@type": "Pharmacy",
                    "name": "<?php echo esc_js($pharmacy->name); ?>",
                    "address": "<?php echo esc_js($pharmacy->address); ?>",
                    "telephone": "<?php echo esc_js($pharmacy->phone); ?>"
                }<?php echo ($index < count($pharmacies) - 1) ? ',' : ''; ?>
                <?php endforeach; ?>
            ]
        }
    }
    </script>
    
    <?php wp_head(); ?>
</head>
<body class="district-page">
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">E</div>
                    <div class="logo-text">
                        <h1><a href="<?php echo home_url(); ?>">İstanbul Nöbetçi Eczaneleri</a></h1>
                        <span class="status">
                            <i class="fas fa-circle"></i>
                            Canlı • <span id="current-date"><?php echo date('d F Y, l', strtotime($selected_date)); ?></span>
                        </span>
                    </div>
                </div>

                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="<?php echo home_url(); ?>#map"><i class="fas fa-map"></i> Harita</a></li>
                        <li><a href="<?php echo home_url(); ?>#pharmacies"><i class="fas fa-pills"></i> Eczaneler</a></li>
                        <li><a href="<?php echo home_url(); ?>#districts"><i class="fas fa-map-marker-alt"></i> İlçeler</a></li>
                    </ul>
                    <div class="social-links">
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </nav>

                <button class="mobile-menu-toggle" aria-label="Menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </header>

    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb-list">
                    <li><a href="<?php echo home_url(); ?>">Ana Sayfa</a></li>
                    <li><a href="<?php echo home_url(); ?>#districts">İlçeler</a></li>
                    <li class="active"><?php echo esc_html($district->name); ?></li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- District Header -->
    <section class="district-header">
        <div class="container">
            <div class="district-info">
                <h1><?php echo esc_html($district->name); ?> Nöbetçi Eczaneleri</h1>
                <p class="district-meta">
                    <i class="fas fa-calendar"></i> 
                    <?php echo date('d F Y, l', strtotime($selected_date)); ?> • 
                    <i class="fas fa-pills"></i> 
                    <?php echo count($pharmacies); ?> Eczane
                </p>
                
                <!-- Date Navigation -->
                <div class="date-navigation">
                    <?php
                    $prev_date = date('Y-m-d', strtotime($selected_date . ' -1 day'));
                    $next_date = date('Y-m-d', strtotime($selected_date . ' +1 day'));
                    $today = date('Y-m-d');
                    ?>
                    
                    <a href="<?php echo home_url('/ilce/' . $district_slug . '/' . $prev_date); ?>" class="date-nav prev">
                        <i class="fas fa-chevron-left"></i> Önceki Gün
                    </a>
                    
                    <?php if ($selected_date !== $today): ?>
                    <a href="<?php echo home_url('/ilce/' . $district_slug); ?>" class="date-nav today">
                        <i class="fas fa-calendar-day"></i> Bugün
                    </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo home_url('/ilce/' . $district_slug . '/' . $next_date); ?>" class="date-nav next">
                        Sonraki Gün <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Pharmacies List -->
    <main class="main-content district-content">
        <div class="container">
            <?php if (empty($pharmacies)): ?>
                <div class="no-pharmacies">
                    <i class="fas fa-search"></i>
                    <h2>Bu tarih için nöbetçi eczane bulunamadı</h2>
                    <p><?php echo esc_html($district->name); ?> ilçesinde <?php echo date('d.m.Y', strtotime($selected_date)); ?> tarihinde nöbetçi eczane bulunmamaktadır.</p>
                    <a href="<?php echo home_url('/ilce/' . $district_slug); ?>" class="btn-primary">Bugünkü Nöbetçiler</a>
                </div>
            <?php else: ?>
                <div class="pharmacies-grid">
                    <?php foreach ($pharmacies as $pharmacy): ?>
                        <div class="pharmacy-card" data-pharmacy='<?php echo esc_attr(json_encode($pharmacy)); ?>'>
                            <div class="pharmacy-header">
                                <h3><?php echo esc_html($pharmacy->name); ?></h3>
                                <span class="pharmacy-status">
                                    <i class="fas fa-circle"></i> Nöbetçi
                                </span>
                            </div>
                            
                            <div class="pharmacy-info">
                                <p class="address">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?php echo esc_html($pharmacy->address); ?>
                                </p>
                                
                                <?php if ($pharmacy->phone): ?>
                                <p class="phone">
                                    <i class="fas fa-phone"></i>
                                    <a href="tel:<?php echo esc_attr(preg_replace('/[^\d+]/', '', $pharmacy->phone)); ?>">
                                        <?php echo esc_html($pharmacy->phone); ?>
                                    </a>
                                </p>
                                <?php endif; ?>
                            </div>
                            
                            <div class="pharmacy-actions">
                                <button class="btn-directions" onclick="getDirections('<?php echo esc_js($pharmacy->address); ?>')">
                                    <i class="fas fa-directions"></i> Yol Tarifi
                                </button>
                                
                                <?php if ($pharmacy->phone): ?>
                                <a href="tel:<?php echo esc_attr(preg_replace('/[^\d+]/', '', $pharmacy->phone)); ?>" class="btn-call">
                                    <i class="fas fa-phone"></i> Ara
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <div class="logo-icon">E</div>
                        <div class="logo-text">
                            <h3>İstanbul Nöbetçi Eczaneleri</h3>
                            <p>Güvenilir ve güncel nöbetçi eczane bilgileri</p>
                        </div>
                    </div>
                </div>

                <div class="footer-section">
                    <h4>Hızlı Erişim</h4>
                    <ul>
                        <li><a href="<?php echo home_url(); ?>#map">Harita</a></li>
                        <li><a href="<?php echo home_url(); ?>#pharmacies">Eczaneler</a></li>
                        <li><a href="<?php echo home_url(); ?>#districts">İlçeler</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>İlçeler</h4>
                    <ul>
                        <?php 
                        $popular_districts = $wpdb->get_results("
                            SELECT * FROM $table_districts 
                            ORDER BY pharmacy_count DESC 
                            LIMIT 6
                        ");
                        foreach ($popular_districts as $pop_district): 
                        ?>
                        <li>
                            <a href="<?php echo home_url('/ilce/' . $pop_district->slug); ?>">
                                <?php echo esc_html($pop_district->name); ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Bizi Takip Edin</h4>
                    <div class="social-links">
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> İstanbul Nöbetçi Eczaneleri. Tüm hakları saklıdır.</p>
                <p>Veriler İstanbul Eczacı Odası'ndan alınmaktadır.</p>
            </div>
        </div>
    </footer>

    <script>
        function getDirections(address) {
            const encodedAddress = encodeURIComponent(address);
            const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`;
            window.open(mapsUrl, '_blank');
        }
    </script>

    <?php wp_footer(); ?>
</body>
</html>
