// DOM Elements
const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
const mobileMenu = document.querySelector('.mobile-menu');
const mobileMenuClose = document.querySelector('.mobile-menu-close');
const currentDateElement = document.getElementById('current-date');
const districtSearch = document.getElementById('district-search');
const useLocationBtn = document.getElementById('use-location');
const searchBtn = document.querySelector('.search-btn');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    updateCurrentDate();
    setupEventListeners();
    setupIntersectionObserver();
    loadPharmacyData();
}

// Update current date
function updateCurrentDate() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    };
    const dateString = now.toLocaleDateString('tr-TR', options);
    if (currentDateElement) {
        currentDateElement.textContent = dateString;
    }
}

// Setup event listeners
function setupEventListeners() {
    // Mobile menu toggle
    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', toggleMobileMenu);

        // Close mobile menu when clicking outside
        mobileMenu.addEventListener('click', function(e) {
            if (e.target === mobileMenu) {
                closeMobileMenu();
            }
        });

        // Close mobile menu with escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
                closeMobileMenu();
            }
        });
    }

    // Mobile menu close button
    if (mobileMenuClose) {
        mobileMenuClose.addEventListener('click', closeMobileMenu);
    }

    // Search functionality
    if (districtSearch) {
        districtSearch.addEventListener('input', handleSearchInput);
        districtSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    if (searchBtn) {
        searchBtn.addEventListener('click', performSearch);
    }

    // Location button
    if (useLocationBtn) {
        useLocationBtn.addEventListener('click', getUserLocation);
    }

    // Pharmacy action buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-directions')) {
            handleDirections(e.target.closest('.pharmacy-item'));
        }

        if (e.target.closest('.btn-call')) {
            handlePhoneCall(e.target.closest('.pharmacy-item'));
        }
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                closeMobileMenu();
            }
        });
    });

    // District item clicks
    document.querySelectorAll('.district-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const districtName = this.querySelector('span').textContent;
            searchByDistrict(districtName);
        });
    });
}

// Mobile menu functions
function toggleMobileMenu() {
    const isActive = mobileMenu.classList.contains('active');

    if (isActive) {
        closeMobileMenu();
    } else {
        openMobileMenu();
    }
}

function openMobileMenu() {
    mobileMenu.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Animate hamburger menu to X
    const spans = mobileMenuToggle.querySelectorAll('span');
    spans.forEach((span, index) => {
        if (index === 0) span.style.transform = 'rotate(45deg) translate(5px, 5px)';
        if (index === 1) span.style.opacity = '0';
        if (index === 2) span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
    });
}

function closeMobileMenu() {
    mobileMenu.classList.remove('active');
    document.body.style.overflow = '';

    // Reset hamburger menu
    const spans = mobileMenuToggle.querySelectorAll('span');
    spans.forEach(span => {
        span.style.transform = '';
        span.style.opacity = '';
    });
}

// Search functionality
function handleSearchInput(e) {
    const query = e.target.value.toLowerCase();

    // Show search suggestions (mock implementation)
    if (query.length > 2) {
        showSearchSuggestions(query);
    } else {
        hideSearchSuggestions();
    }
}

function showSearchSuggestions(query) {
    // Mock district suggestions
    const districts = [
        'Kadıköy', 'Beşiktaş', 'Üsküdar', 'Şişli', 'Fatih', 'Bakırköy',
        'Beyoğlu', 'Ataşehir', 'Maltepe', 'Pendik', 'Kartal', 'Tuzla'
    ];

    const filtered = districts.filter(district =>
        district.toLowerCase().includes(query)
    );

    // Create suggestions dropdown (implementation would go here)
    console.log('Suggestions:', filtered);
}

function hideSearchSuggestions() {
    // Hide suggestions dropdown
}

function performSearch() {
    const query = districtSearch.value.trim();
    if (query) {
        searchByDistrict(query);
    } else {
        showNotification('Lütfen bir ilçe adı girin', 'warning');
    }
}

function searchByDistrict(districtName) {
    showLoading();

    // Mock API call
    setTimeout(() => {
        hideLoading();
        updatePharmacyList(districtName);
        showNotification(`${districtName} için nöbetçi eczaneler listelendi`, 'success');

        // Scroll to results
        const pharmaciesSection = document.getElementById('pharmacies');
        if (pharmaciesSection) {
            pharmaciesSection.scrollIntoView({ behavior: 'smooth' });
        }
    }, 1000);
}

// Geolocation functionality
function getUserLocation() {
    if (!navigator.geolocation) {
        showNotification('Tarayıcınız konum servisini desteklemiyor', 'error');
        return;
    }

    showLoading();
    useLocationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Konum alınıyor...';

    navigator.geolocation.getCurrentPosition(
        function(position) {
            const { latitude, longitude } = position.coords;
            findNearestPharmacies(latitude, longitude);
        },
        function(error) {
            hideLoading();
            useLocationBtn.innerHTML = '<i class="fas fa-location-arrow"></i> Konumumu Kullan';

            let message = 'Konum alınamadı';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    message = 'Konum izni reddedildi';
                    break;
                case error.POSITION_UNAVAILABLE:
                    message = 'Konum bilgisi mevcut değil';
                    break;
                case error.TIMEOUT:
                    message = 'Konum alma zaman aşımı';
                    break;
            }
            showNotification(message, 'error');
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000
        }
    );
}

function findNearestPharmacies(lat, lng) {
    // Mock API call to find nearest pharmacies
    setTimeout(() => {
        hideLoading();
        useLocationBtn.innerHTML = '<i class="fas fa-location-arrow"></i> Konumumu Kullan';

        // Update pharmacy list with nearest ones
        updatePharmacyListByLocation(lat, lng);
        showNotification('Konumunuza en yakın eczaneler listelendi', 'success');

        // Scroll to results
        const pharmaciesSection = document.getElementById('pharmacies');
        if (pharmaciesSection) {
            pharmaciesSection.scrollIntoView({ behavior: 'smooth' });
        }
    }, 1500);
}

// Pharmacy data management
function loadPharmacyData() {
    // Mock pharmacy data
    const mockPharmacies = [
        {
            name: 'Acıbadem Eczanesi',
            address: 'Acıbadem Mah. Çeçen Sok. No:5/A, Üsküdar',
            phone: '(216) 123 45 67',
            district: 'Üsküdar',
            coordinates: { lat: 41.0082, lng: 29.0181 }
        },
        {
            name: 'Beşiktaş Merkez Eczanesi',
            address: 'Barbaros Mah. Beşiktaş Cad. No:12, Beşiktaş',
            phone: '(212) 987 65 43',
            district: 'Beşiktaş',
            coordinates: { lat: 41.0429, lng: 29.0094 }
        },
        {
            name: 'Kadıköy Sağlık Eczanesi',
            address: 'Moda Mah. Bahariye Cad. No:45, Kadıköy',
            phone: '(216) 555 12 34',
            district: 'Kadıköy',
            coordinates: { lat: 40.9897, lng: 29.0322 }
        }
    ];

    window.pharmacyData = mockPharmacies;
}

function updatePharmacyList(districtName) {
    const pharmacyList = document.querySelector('.pharmacy-list');
    if (!pharmacyList) return;

    // Filter pharmacies by district
    const filteredPharmacies = window.pharmacyData.filter(pharmacy =>
        pharmacy.district.toLowerCase().includes(districtName.toLowerCase())
    );

    if (filteredPharmacies.length === 0) {
        pharmacyList.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>${districtName} için nöbetçi eczane bulunamadı.</p>
            </div>
        `;
        return;
    }

    pharmacyList.innerHTML = filteredPharmacies.map(pharmacy => `
        <div class="pharmacy-item" data-pharmacy='${JSON.stringify(pharmacy)}'>
            <div class="pharmacy-info">
                <h3>${pharmacy.name}</h3>
                <p><i class="fas fa-map-marker-alt"></i> ${pharmacy.address}</p>
                <p><i class="fas fa-phone"></i> ${pharmacy.phone}</p>
            </div>
            <div class="pharmacy-actions">
                <button class="btn-directions"><i class="fas fa-directions"></i> Yol Tarifi</button>
                <button class="btn-call"><i class="fas fa-phone"></i> Ara</button>
            </div>
        </div>
    `).join('');
}

function updatePharmacyListByLocation(lat, lng) {
    // Calculate distances and sort by nearest
    const pharmaciesWithDistance = window.pharmacyData.map(pharmacy => ({
        ...pharmacy,
        distance: calculateDistance(lat, lng, pharmacy.coordinates.lat, pharmacy.coordinates.lng)
    })).sort((a, b) => a.distance - b.distance);

    const pharmacyList = document.querySelector('.pharmacy-list');
    if (!pharmacyList) return;

    pharmacyList.innerHTML = pharmaciesWithDistance.slice(0, 5).map(pharmacy => `
        <div class="pharmacy-item" data-pharmacy='${JSON.stringify(pharmacy)}'>
            <div class="pharmacy-info">
                <h3>${pharmacy.name}</h3>
                <p><i class="fas fa-map-marker-alt"></i> ${pharmacy.address}</p>
                <p><i class="fas fa-phone"></i> ${pharmacy.phone}</p>
                <p><i class="fas fa-route"></i> ${pharmacy.distance.toFixed(1)} km uzaklıkta</p>
            </div>
            <div class="pharmacy-actions">
                <button class="btn-directions"><i class="fas fa-directions"></i> Yol Tarifi</button>
                <button class="btn-call"><i class="fas fa-phone"></i> Ara</button>
            </div>
        </div>
    `).join('');
}

// Utility functions
function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
}

function handleDirections(pharmacyItem) {
    const pharmacyData = JSON.parse(pharmacyItem.dataset.pharmacy);
    const address = encodeURIComponent(pharmacyData.address);

    // Open Google Maps with directions
    const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${address}`;
    window.open(mapsUrl, '_blank');

    // Track analytics event
    trackEvent('directions_clicked', {
        pharmacy_name: pharmacyData.name,
        district: pharmacyData.district
    });
}

function handlePhoneCall(pharmacyItem) {
    const pharmacyData = JSON.parse(pharmacyItem.dataset.pharmacy);
    const phoneNumber = pharmacyData.phone.replace(/[^\d+]/g, '');

    // Create phone call link
    window.location.href = `tel:${phoneNumber}`;

    // Track analytics event
    trackEvent('phone_call_clicked', {
        pharmacy_name: pharmacyData.name,
        district: pharmacyData.district
    });
}

// Loading states
function showLoading() {
    const loadingElements = document.querySelectorAll('.loading-target');
    loadingElements.forEach(el => el.classList.add('loading'));
}

function hideLoading() {
    const loadingElements = document.querySelectorAll('.loading-target');
    loadingElements.forEach(el => el.classList.remove('loading'));
}

// Notifications
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        removeNotification(notification);
    }, 5000);

    // Close button functionality
    notification.querySelector('.notification-close').addEventListener('click', () => {
        removeNotification(notification);
    });

    // Animate in
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
}

function removeNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || icons.info;
}

// Intersection Observer for animations
function setupIntersectionObserver() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.content-section, .stat-item, .feature-item').forEach(el => {
        observer.observe(el);
    });
}

// Analytics tracking (mock implementation)
function trackEvent(eventName, parameters = {}) {
    // Google Analytics 4 event tracking
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, parameters);
    }

    // Console log for development
    console.log('Analytics Event:', eventName, parameters);
}

// Performance monitoring
function measurePerformance() {
    if ('performance' in window) {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                const loadTime = perfData.loadEventEnd - perfData.loadEventStart;

                trackEvent('page_load_time', {
                    load_time: Math.round(loadTime),
                    page_type: 'homepage'
                });
            }, 0);
        });
    }
}

// Initialize performance monitoring
measurePerformance();

// Service Worker registration for PWA capabilities
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Add notification styles dynamically
const notificationStyles = `
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
    }

    .notification.show {
        transform: translateX(0);
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 1rem;
    }

    .notification-success {
        border-left: 4px solid #10b981;
    }

    .notification-error {
        border-left: 4px solid #ef4444;
    }

    .notification-warning {
        border-left: 4px solid #f59e0b;
    }

    .notification-info {
        border-left: 4px solid #3b82f6;
    }

    .notification-close {
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        margin-left: auto;
    }

    .notification-close:hover {
        background: #f3f4f6;
    }

    .no-results {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }

    .no-results i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #d1d5db;
    }

    @media (max-width: 768px) {
        .notification {
            top: 10px;
            right: 10px;
            left: 10px;
            max-width: none;
            transform: translateY(-100%);
        }

        .notification.show {
            transform: translateY(0);
        }
    }
`;

// Inject notification styles
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);

// Error handling
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    trackEvent('javascript_error', {
        error_message: e.message,
        error_filename: e.filename,
        error_line: e.lineno
    });
});

// Unhandled promise rejection handling
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
    trackEvent('promise_rejection', {
        error_message: e.reason.toString()
    });
});