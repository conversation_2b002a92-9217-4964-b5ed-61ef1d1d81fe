<?php
/**
 * Test Data Import Script
 * 
 * This file is for testing purposes only.
 * Run this once to import sample data from JSON files.
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>Testing Data Import</h1>";

// Test database tables creation
echo "<h2>Creating Database Tables...</h2>";
eczane_create_tables();
echo "✓ Tables created successfully<br>";

// Test JSON file processing
echo "<h2>Processing JSON Files...</h2>";

$scripts_path = ABSPATH . 'scripts/';
$json_files = glob($scripts_path . 'istanbul_pharmacies_*.json');

if (empty($json_files)) {
    echo "❌ No JSON files found in scripts directory<br>";
} else {
    foreach ($json_files as $file) {
        echo "Processing: " . basename($file) . "<br>";
        $result = eczane_process_json_file($file);
        if ($result) {
            echo "✓ Successfully processed " . basename($file) . "<br>";
        } else {
            echo "❌ Failed to process " . basename($file) . "<br>";
        }
    }
}

// Update district counts
echo "<h2>Updating District Counts...</h2>";
eczane_update_district_counts();
echo "✓ District counts updated<br>";

// Display results
echo "<h2>Import Results:</h2>";

global $wpdb;
$table_pharmacies = $wpdb->prefix . 'eczane_pharmacies';
$table_districts = $wpdb->prefix . 'eczane_districts';

$pharmacy_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_pharmacies");
$district_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_districts");

echo "Total Pharmacies: <strong>$pharmacy_count</strong><br>";
echo "Total Districts: <strong>$district_count</strong><br>";

// Show sample districts
echo "<h3>Sample Districts:</h3>";
$districts = $wpdb->get_results("SELECT * FROM $table_districts ORDER BY pharmacy_count DESC LIMIT 10");

if ($districts) {
    echo "<ul>";
    foreach ($districts as $district) {
        $url = home_url('/ilce/' . $district->slug);
        echo "<li><a href='$url' target='_blank'>{$district->name}</a> ({$district->pharmacy_count} eczane)</li>";
    }
    echo "</ul>";
} else {
    echo "No districts found.<br>";
}

// Test rewrite rules
echo "<h2>Testing Rewrite Rules...</h2>";
flush_rewrite_rules();
echo "✓ Rewrite rules flushed<br>";

echo "<h2>Test Complete!</h2>";
echo "<p><a href='" . home_url() . "'>Visit Homepage</a></p>";

// Show some sample URLs to test
if ($districts) {
    echo "<h3>Test URLs:</h3>";
    echo "<ul>";
    foreach (array_slice($districts, 0, 5) as $district) {
        $url = home_url('/ilce/' . $district->slug);
        echo "<li><a href='$url' target='_blank'>$url</a></li>";
    }
    echo "</ul>";
}
?>
